import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import React, { useMemo, useState } from 'react';
import styled, { css } from 'styled-components';
import { TextConfig } from '../../Engine/models/text.config';
import { FormBuilder } from '../form-builder';
import { Text } from '../Text';

export type ColumnConfig<T> = {
  key: keyof T;
  header?: string | TextConfig;
  cell?: (value: any, row: T, onChange?: (val: any) => void) => React.ReactNode;
  filterable?: boolean;
  sortable?: boolean;
  width?: string;
  headerTextConfig?: TextConfig;
  footerTextConfig?: TextConfig;
  bodyTextConfig?: (value: any, row: T) => TextConfig;
  controlType?: 'checkbox' | 'plain-text';
  controlProps?: Record<string, any>;
  onControlChange?: (value: any, row: T) => void;
  customControlName?: string;
};

// Props for the dynamic table
interface DynamicTableProps<T extends Record<string, any>> {
  data: T[];
  columnConfig?: Partial<Record<keyof T, ColumnConfig<T>>>;
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enablePagination?: boolean;
  enableInfoText?: boolean;
  pageSize?: number;
  className?: string;
  tableBorder?: string; // New prop for border styling
}

// Styled Components
const TableWrapper = styled.div<{ border?: string }>`
  overflow-x: auto;
  border-radius: 8px;
  ${({ border }) =>
    border
      ? css`
          border: ${border};
        `
      : ''}
`;

const StyledTable = styled.table`
  min-width: 100%;
  border-collapse: collapse;
`;

const Thead = styled.thead`
  /* background: #f9fafb; */
`;

const Th = styled.th`
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  /* color: #6b7280; */
  text-transform: uppercase;
  letter-spacing: 0.05em;
  /* background: #f9fafb; */
  position: relative;
`;

const FilterInput = styled.input`
  margin-top: 4px;
  width: 100%;
  padding: 4px 8px;
  font-size: 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  box-sizing: border-box;
`;

const Tbody = styled.tbody`
  /* background: #fff; */
`;

const Tr = styled.tr`
  &:hover {
    /* background: #f3f4f6; */
  }
`;

const Td = styled.td`
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  /* color: #111827; */
`;

const PaginationWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
`;

const PaginationButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PaginationButton = styled.button`
  padding: 4px 12px;
  font-size: 14px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  /* background: #fff; */
  cursor: pointer;
  transition: background 0.2s;
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PageInfo = styled.span`
  font-size: 14px;
  /* color: #374151; */
`;

const PageSizeSelect = styled.select`
  padding: 4px 8px;
  font-size: 14px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  /* background: #fff; */
`;

const GlobalFilterInput = styled.input`
  padding: 8px 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
  width: 240px;
  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #bfdbfe;
  }
`;

const InfoText = styled.div`
  font-size: 13px;
  /* color: #6b7280; */
  margin-top: 8px;
  text-align: left;
`;

const NoData = styled.div`
  text-align: center;
  padding: 32px 0;
  /* color: #9ca3af; */
  font-size: 16px;
`;

// Helper function to determine column type and render appropriate cell
const renderCell = <T extends Record<string, any>>(
  value: any,
  config?: ColumnConfig<T>,
  row?: T
): React.ReactNode => {
  // If a cell render function is provided, use it
  if (config?.cell) {
    return config.cell(
      value,
      row!,
      config.onControlChange
        ? (val: any) => config.onControlChange!(val, row!)
        : undefined
    );
  }

  // Render form controls if specified using FormBuilder
  if (config?.controlType && row) {
    const controlName = config.key as keyof T as string;
    const controlConfig: any = {
      name: config?.customControlName || controlName,
      type: config.controlType,
      ...(config.controlProps || {}),
      // For checkboxes, FormBuilder expects value, not checked
      value: config.controlType === 'checkbox' ? !!value : value,
      onChange: (val: any) => config.onControlChange?.(val, row),
    };

    const formConfig = {
      controls: [controlConfig],
      style: { margin: 0, padding: 0 },
    };
    const defaultValues = { [controlName]: value };

    return (
      <FormBuilder
        config={formConfig}
        defaultValues={defaultValues}
        isStory={true}
      />
    );
  }

  // If a bodyTextConfig is provided, use it for Text rendering
  if (config?.bodyTextConfig) {
    const textConfig = config.bodyTextConfig(value, row!);
    return <Text textItems={[textConfig]} />;
  }

  // Fallbacks
  if (value === null || value === undefined) {
    return (
      <Text
        textItems={[
          {
            text: '-',
            options: {
              format: 'paragraph',
              type: 'value',
            },
          },
        ]}
      />
    );
  }

  if (typeof value === 'boolean') {
    return (
      <Text
        textItems={[
          {
            text: value ? 'Yes' : 'No',
            options: {
              format: 'paragraph',
              type: 'value',
              style: {
                padding: '2px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: 500,
                background: value ? '#dcfce7' : '#fee2e2',
                color: value ? '#166534' : '#991b1b',
              },
            },
          },
        ]}
      />
    );
  }

  if (typeof value === 'number') {
    return (
      <Text
        textItems={[
          {
            text: value.toLocaleString(),
            options: {
              format: 'paragraph',
              type: 'value',
              style: { fontFamily: 'monospace' },
            },
          },
        ]}
      />
    );
  }

  if (value instanceof Date) {
    return (
      <Text
        textItems={[
          {
            text: value.toLocaleDateString(),
            options: {
              format: 'paragraph',
              type: 'value',
              style: { fontFamily: 'monospace' },
            },
          },
        ]}
      />
    );
  }

  if (typeof value === 'string' && value.startsWith('http')) {
    return (
      <Text
        textItems={[
          {
            text: 'Link',
            options: {
              format: 'paragraph',
              type: 'value',
              style: { color: '#2563eb', textDecoration: 'underline' },
            },
          },
        ]}
      />
    );
  }

  if (typeof value === 'object') {
    return (
      <Text
        textItems={[
          {
            text: JSON.stringify(value, null, 2),
            options: {
              format: 'paragraph',
              type: 'value',
              style: {
                fontSize: '12px',
                background: '#f3f4f6',
                padding: '4px',
                borderRadius: '4px',
                maxWidth: '320px',
                overflow: 'hidden',
              },
            },
          },
        ]}
      />
    );
  }

  return (
    <Text
      textItems={[
        {
          text: String(value),
          options: {
            format: 'paragraph',
            type: 'value',
          },
        },
      ]}
    />
  );
};

// Helper function to render header with Text if config provided
const renderHeader = (header: string | TextConfig | undefined) => {
  if (!header) return null;
  if (typeof header === 'string') {
    return (
      <Text
        textItems={[
          {
            text: header,
            options: { format: 'heading' },
          },
        ]}
      />
    );
  }
  return <Text textItems={[header]} />;
};

// Main dynamic table component
export function DynamicTable<T extends Record<string, any>>({
  data,
  columnConfig = {},
  enableSorting = false,
  enableFiltering = false,
  enablePagination = false,
  enableInfoText = false,
  pageSize = 10,
  className = '',
  tableBorder,
}: DynamicTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize,
  });

  // Auto-generate columns based on data
  const columns = useMemo<ColumnDef<T>[]>(() => {
    if (!data || data.length === 0) return [];

    const sampleRow = data[0];
    const keys = Object.keys(sampleRow) as (keyof T)[];

    return keys.map((key) => {
      const config = columnConfig[key];
      const keyStr = String(key);

      return {
        accessorKey: key,
        header: () =>
          config?.headerTextConfig ? (
            <Text textItems={[config.headerTextConfig]} />
          ) : (
            renderHeader(config?.header)
          ),
        cell: ({ getValue, row }) =>
          renderCell(getValue(), config, row.original),
        enableSorting: config?.sortable !== false && enableSorting,
        enableColumnFilter: config?.filterable !== false && enableFiltering,
        size: config?.width ? parseInt(config.width) : undefined,
        footer: config?.footerTextConfig
          ? () => <Text textItems={[config.footerTextConfig!]} />
          : undefined,
      };
    });
  }, [data, columnConfig, enableSorting, enableFiltering]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      columnFilters,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    enableSorting,
    enableFilters: enableFiltering,
  });

  if (!data || data.length === 0) {
    return <NoData>No data available</NoData>;
  }

  return (
    <div className={className} style={{ width: '100%' }}>
      {/* Global Filter */}
      {enableFiltering && (
        <GlobalFilterInput
          type="text"
          placeholder="Search all columns..."
          onChange={(e) => {
            const value = e.target.value;
            table.setGlobalFilter(value);
          }}
        />
      )}

      {/* Table */}
      <TableWrapper border={tableBorder}>
        <StyledTable>
          <Thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <Tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <Th key={header.id} style={{ width: header.getSize() }}>
                    <div
                      style={{ display: 'flex', alignItems: 'center', gap: 8 }}
                    >
                      <span
                        style={{
                          cursor: header.column.getCanSort()
                            ? 'pointer'
                            : 'default',
                          userSelect: header.column.getCanSort()
                            ? 'none'
                            : 'auto',
                        }}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </span>
                      {header.column.getCanSort() && (
                        <span style={{ color: '#9ca3af' }}>
                          {{
                            asc: ' 🔼',
                            desc: ' 🔽',
                          }[header.column.getIsSorted() as string] ?? ' ↕️'}
                        </span>
                      )}
                    </div>
                    {header.column.getCanFilter() && (
                      <FilterInput
                        type="text"
                        placeholder="Filter..."
                        value={(header.column.getFilterValue() as string) ?? ''}
                        onChange={(e) =>
                          header.column.setFilterValue(e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                  </Th>
                ))}
              </Tr>
            ))}
          </Thead>
          <Tbody>
            {table.getRowModel().rows.map((row) => (
              <Tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Td key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Td>
                ))}
              </Tr>
            ))}
          </Tbody>
        </StyledTable>
      </TableWrapper>

      {/* Pagination */}
      {enablePagination && (
        <PaginationWrapper>
          <PaginationButtons>
            <PaginationButton
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              {'<<'}
            </PaginationButton>
            <PaginationButton
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              {'<'}
            </PaginationButton>
            <PaginationButton
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              {'>'}
            </PaginationButton>
            <PaginationButton
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              {'>>'}
            </PaginationButton>
          </PaginationButtons>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <PageInfo>
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </PageInfo>
            <PageSizeSelect
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  Show {pageSize}
                </option>
              ))}
            </PageSizeSelect>
          </div>
        </PaginationWrapper>
      )}

      {/* Info */}
      {enableInfoText && (
        <InfoText>
          Showing {table.getRowModel().rows.length} of{' '}
          {table.getPreFilteredRowModel().rows.length} rows
        </InfoText>
      )}
    </div>
  );
}
