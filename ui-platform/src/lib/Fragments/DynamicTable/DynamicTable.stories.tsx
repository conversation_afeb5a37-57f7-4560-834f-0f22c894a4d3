import React, { useState } from 'react';
import { TextConfig } from '../../Engine/models/text.config';
import { ColumnConfig, DynamicTable } from './DynamicTable';

type User = {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
};

const initialData: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    isActive: true,
    createdAt: '2024-06-01',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    isActive: false,
    createdAt: '2024-05-15',
  },
];

export default {
  title: 'Fragments/DynamicTable',
  component: DynamicTable,
};

export const WithCheckbox = () => {
  const [data, setData] = useState<User[]>(initialData);

  const handleCheckboxChange = (checked: boolean, row: User) => {
    setData((prev) =>
      prev.map((item) =>
        item.id === row.id ? { ...item, isActive: checked } : item
      )
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name',
      headerTextConfig: {
        text: 'Full Name',
        options: { format: 'heading' },
      } as TextConfig,
      // Example: render as input field
      controlType: 'input',
      controlProps: { style: { width: 140 } },
      onControlChange: (val, row) => {
        setData((prev) =>
          prev.map((item) =>
            item.id === row.id ? { ...item, name: val } : item
          )
        );
      },
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'heading' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'heading' },
      } as TextConfig,
      controlType: 'checkbox',
      onControlChange: (val, row) => handleCheckboxChange(val, row),
      controlProps: { 'aria-label': 'Active Checkbox' },
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  return (
    <DynamicTable
      data={data}
      columnConfig={columnConfig}
      pageSize={5}
      enableFiltering
      enableSorting
      enablePagination
      enableInfoText
      tableBorder="1px solid #e5e7eb"
    />
  );
};

// New story: WithInput
export const WithInput = () => {
  const [data, setData] = useState<User[]>(initialData);

  const handleNameChange = (val: string, row: User) => {
    setData((prev) =>
      prev.map((item) => (item.id === row.id ? { ...item, name: val } : item))
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name (Editable)',
      headerTextConfig: {
        text: 'Full Name (Edit)',
        options: { format: 'heading' },
      } as TextConfig,
      controlType: 'plain-text',
      controlProps: { style: { width: 180 } },
      customControlName: 'full_name',
      onControlChange: handleNameChange,
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'heading' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'heading' },
      } as TextConfig,
      // Just display as text in this story
      bodyTextConfig: (value: boolean) => ({
        text: value ? 'Yes' : 'No',
        options: {
          format: 'paragraph',
          style: {
            color: value ? '#166534' : '#991b1b',
            background: value ? '#dcfce7' : '#fee2e2',
            borderRadius: '4px',
            padding: '2px 8px',
            fontWeight: 500,
          },
        },
      }),
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  return (
    <DynamicTable
      data={data}
      columnConfig={columnConfig}
      pageSize={5}
      enableFiltering
      enableSorting
      enablePagination
      enableInfoText
      tableBorder="1px solid #e5e7eb"
    />
  );
};

// New story: WithInputArray
export const WithInputArray = () => {
  // Store name as an array of values
  const [nameValues, setNameValues] = useState(initialData.map((u) => u.name));
  const [data, setData] = useState<User[]>(initialData);

  // Update the array at the correct index
  const handleNameChange = (val: string, row: User, rowIndex: number) => {
    setNameValues((prev) => {
      const updated = [...prev];
      updated[rowIndex] = val;
      return updated;
    });
    setData((prev) =>
      prev.map((item, idx) =>
        idx === rowIndex ? { ...item, name: val } : item
      )
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name (Editable)',
      headerTextConfig: {
        text: 'Full Name (Edit)',
        options: { format: 'heading' },
      } as TextConfig,
      controlType: 'plain-text',
      controlProps: { style: { width: 180 } },
      customControlName: 'full_name',
      // Pass rowIndex to onControlChange
      onControlChange: (val, row) => {
        const rowIndex = data.findIndex((item) => item.id === row.id);
        handleNameChange(val, row, rowIndex);
      },
      // Use value from the array for each row
      cell: (_value, row) => {
        const rowIndex = data.findIndex((item) => item.id === row.id);
        return nameValues[rowIndex];
      },
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'heading' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: boolean) => ({
        text: value ? 'Yes' : 'No',
        options: {
          format: 'paragraph',
          style: {
            color: value ? '#166534' : '#991b1b',
            background: value ? '#dcfce7' : '#fee2e2',
            borderRadius: '4px',
            padding: '2px 8px',
            fontWeight: 500,
          },
        },
      }),
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  // Provide the value for each row from the array
  const tableData = data.map((row, idx) => ({
    ...row,
    name: nameValues[idx],
  }));

  return (
    <DynamicTable
      data={tableData}
      columnConfig={columnConfig}
      pageSize={5}
      enableFiltering
      enableSorting
      enablePagination
      enableInfoText
      tableBorder="1px solid #e5e7eb"
    />
  );
};
